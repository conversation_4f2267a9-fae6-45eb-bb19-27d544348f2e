'use client'

import React, { memo, useMemo, useCallback, useRef, useEffect, useState } from 'react'
import { <PERSON>rk<PERSON>, ChevronUp, ChevronDown, MapPin, Target, Eye, EyeOff } from 'lucide-react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import { useSmartScrollFollow } from '@/hooks/useSmartScrollFollow'
import { NoteReference, TextSegment } from '@/lib/store'
import { createBidirectionalNavigator, NavigationCallbacks } from '@/lib/bidirectionalNavigator'

interface EnhancedStructuredNotesProps {
  content: string
  streamingContent?: string
  isAnalyzing?: boolean
  isExpanded: boolean
  height: number
  onToggleExpanded: () => void
  onManualExpand: () => void
  onManualCollapse: () => void
  className?: string
  fullscreen?: boolean
  
  // 增强功能属性
  noteReferences?: NoteReference[]
  textSegments?: TextSegment[]
  onSegmentHighlight?: (segmentIds: string[]) => void
  onScrollToSegment?: (segmentId: string) => void
}

/**
 * 增强的结构化笔记组件
 * 支持隐藏定位标记和双向导航
 */
const EnhancedStructuredNotes = memo<EnhancedStructuredNotesProps>(({
  content,
  streamingContent,
  isAnalyzing,
  isExpanded,
  height,
  onToggleExpanded,
  onManualExpand,
  onManualCollapse,
  className = '',
  fullscreen = false,
  noteReferences = [],
  textSegments = [],
  onSegmentHighlight,
  onScrollToSegment
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [showLocationIndicators, setShowLocationIndicators] = useState(true)
  const [hoveredNoteId, setHoveredNoteId] = useState<string | null>(null)
  const [activeNoteIds, setActiveNoteIds] = useState<string[]>([])
  const [tooltip, setTooltip] = useState<{ content: string, x: number, y: number } | null>(null)
  
  // 智能滚动跟随
  const { shouldAutoScroll } = useSmartScrollFollow(contentRef, !!streamingContent)

  // 显示的内容（优先显示流式内容）
  const displayContent = streamingContent || content

  // 创建双向导航器
  const navigator = useMemo(() => {
    const callbacks: NavigationCallbacks = {
      onSegmentHighlight: (segmentIds) => {
        onSegmentHighlight?.(segmentIds)
      },
      onNoteHighlight: (noteIds) => {
        setActiveNoteIds(noteIds)
      },
      onScrollToSegment: (segmentId) => {
        onScrollToSegment?.(segmentId)
      },
      onShowTooltip: (content, position) => {
        setTooltip({ content, x: position.x, y: position.y })
      },
      onHideTooltip: () => {
        setTooltip(null)
      }
    }

    const nav = createBidirectionalNavigator(callbacks)
    nav.initialize(noteReferences, textSegments)
    return nav
  }, [noteReferences, textSegments, onSegmentHighlight, onScrollToSegment])

  // 解析笔记内容，识别可点击的片段
  const parseNotesForInteraction = useCallback((noteContent: string) => {
    const lines = noteContent.split('\n')
    const elements: React.ReactNode[] = []
    let lineIndex = 0

    for (const line of lines) {
      const trimmedLine = line.trim()
      
      if (!trimmedLine) {
        elements.push(<br key={lineIndex} />)
        lineIndex++
        continue
      }

      // 查找与当前行相关的笔记引用
      const relatedNoteRef = noteReferences.find(ref => 
        ref.noteContent.includes(trimmedLine.replace(/[#*\-\[\]]/g, '').trim()) ||
        trimmedLine.includes(ref.noteContent.substring(0, 20))
      )

      const isClickable = !!relatedNoteRef
      const isActive = activeNoteIds.includes(relatedNoteRef?.noteId || '')
      const isHovered = hoveredNoteId === relatedNoteRef?.noteId

      let lineClassName = 'block transition-all duration-200 '
      
      if (isClickable) {
        lineClassName += 'cursor-pointer hover:bg-blue-50 border-l-2 border-transparent hover:border-blue-400 pl-3 -ml-3 rounded-r-md '
      }
      
      if (isActive) {
        lineClassName += 'bg-blue-100 border-blue-500 '
      }
      
      if (isHovered) {
        lineClassName += 'bg-blue-200 '
      }

      elements.push(
        <div
          key={lineIndex}
          className={lineClassName}
          onClick={(e) => isClickable && handleNoteClick(relatedNoteRef!.noteId, e)}
          onMouseEnter={() => isClickable && handleNoteHover(relatedNoteRef!.noteId)}
          onMouseLeave={() => handleNoteHover(null)}
          data-note-id={relatedNoteRef?.noteId}
        >
          <span dangerouslySetInnerHTML={{ __html: line }} />
          
          {/* 定位指示器 */}
          {isClickable && showLocationIndicators && (
            <span className="inline-flex items-center ml-2 text-blue-500 opacity-60 hover:opacity-100 transition-opacity">
              <Target className="w-3 h-3" />
              <span className="text-xs ml-1">
                {relatedNoteRef?.sourceSegments.length || 0}
              </span>
            </span>
          )}
        </div>
      )

      lineIndex++
    }

    return elements
  }, [noteReferences, activeNoteIds, hoveredNoteId, showLocationIndicators])

  // 处理笔记点击
  const handleNoteClick = useCallback((noteId: string, event: React.MouseEvent) => {
    navigator.handleNoteClick(noteId, event.nativeEvent)
  }, [navigator])

  // 处理笔记悬停
  const handleNoteHover = useCallback((noteId: string | null) => {
    setHoveredNoteId(noteId)
    if (noteId) {
      navigator.handleNoteHover(noteId)
    } else {
      navigator.clearHover()
    }
  }, [navigator])

  // 自动滚动到底部（流式内容时）
  useEffect(() => {
    if (shouldAutoScroll && contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight
    }
  }, [displayContent, shouldAutoScroll])

  // 渲染内容
  const renderContent = useCallback(() => {
    if (isAnalyzing && !displayContent) {
      return (
        <div className="h-full flex items-center justify-center">
          <ModernLoader variant="dots" size="lg" text="正在生成智能笔记..." className="text-center" />
        </div>
      )
    }

    if (!displayContent) {
      return (
        <div className="h-full flex items-center justify-center text-gray-500">
          暂无结构化笔记
        </div>
      )
    }

    // 全屏模式下的内容渲染
    if (fullscreen) {
      return (
        <div className="h-full overflow-y-auto">
          {noteReferences.length > 0 ? (
            <div className="space-y-1 prose prose-sm max-w-none">
              {parseNotesForInteraction(displayContent)}
            </div>
          ) : (
            <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
              {displayContent}
            </SafeMarkdown>
          )}
        </div>
      )
    }

    // 卡片模式下的内容渲染
    return (
      <div className="bg-gradient-to-br from-slate-50/50 to-blue-50/30 rounded-xl p-4 relative">
        {noteReferences.length > 0 ? (
          <div className="space-y-1 prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900">
            {parseNotesForInteraction(displayContent)}
          </div>
        ) : (
          <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
            {displayContent}
          </SafeMarkdown>
        )}
      </div>
    )
  }, [isAnalyzing, displayContent, fullscreen, noteReferences, parseNotesForInteraction])

  // 获取统计信息
  const statistics = useMemo(() => {
    return navigator.getStatistics()
  }, [navigator])

  // 全屏模式渲染
  if (fullscreen) {
    return (
      <div className={`h-full ${className}`}>
        {/* 控制栏 */}
        {noteReferences.length > 0 && (
          <div className="flex items-center justify-between mb-4 p-3 bg-white rounded-lg border border-gray-200">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span className="flex items-center space-x-1">
                <MapPin className="w-4 h-4" />
                <span>{statistics.mappedNotes} 个定位点</span>
              </span>
              <span>置信度: {Math.round(statistics.averageConfidence * 100)}%</span>
            </div>
            
            <button
              onClick={() => setShowLocationIndicators(!showLocationIndicators)}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors"
            >
              {showLocationIndicators ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              <span>{showLocationIndicators ? '隐藏' : '显示'}指示器</span>
            </button>
          </div>
        )}
        
        {renderContent()}
      </div>
    )
  }

  // 卡片模式渲染
  return (
    <>
      <div
        className={`absolute top-4 left-4 right-4 bg-white backdrop-blur-lg rounded-3xl border border-slate-200/50 shadow-2xl shadow-slate-200/40 transition-all duration-300 ease-in-out ${className}`}
        style={{
          height: isExpanded ? `${height}%` : '60px',
          zIndex: 60,
          minHeight: '60px',
          maxHeight: '90%'
        }}
      >
        {/* 卡片头部 */}
        <div className="px-5 py-4 flex items-center justify-between border-b border-slate-200/50">
          <div className="flex items-center space-x-3">
            <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
              <Sparkles className="w-4 h-4 text-white" />
            </div>
            <h3 className="font-semibold text-slate-800 text-sm">智能笔记</h3>
            
            {/* 定位功能指示 */}
            {noteReferences.length > 0 && (
              <div className="flex items-center space-x-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
                <Target className="w-3 h-3" />
                <span>{statistics.mappedNotes}</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {streamingContent && (
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            )}
            
            {noteReferences.length > 0 && (
              <button
                onClick={() => setShowLocationIndicators(!showLocationIndicators)}
                className="p-1.5 hover:bg-slate-100 rounded-lg transition-colors"
                title={showLocationIndicators ? '隐藏定位指示器' : '显示定位指示器'}
              >
                {showLocationIndicators ? (
                  <EyeOff className="w-4 h-4 text-slate-600" />
                ) : (
                  <Eye className="w-4 h-4 text-slate-600" />
                )}
              </button>
            )}
            
            <button
              onClick={isExpanded ? onManualCollapse : onManualExpand}
              className="p-1.5 hover:bg-slate-100 rounded-lg transition-colors"
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4 text-slate-600" />
              ) : (
                <ChevronDown className="w-4 h-4 text-slate-600" />
              )}
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div
          ref={contentRef}
          className="px-5 py-4 overflow-y-auto"
          style={{
            height: isExpanded ? 'calc(100% - 73px)' : '0px',
            opacity: isExpanded ? 1 : 0
          }}
        >
          {renderContent()}
        </div>
      </div>

      {/* 悬停提示 */}
      {tooltip && (
        <div
          className="fixed z-[100] p-3 bg-gray-800 text-white text-sm rounded-lg shadow-xl max-w-xs pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 10,
            transform: 'translateY(-100%)'
          }}
          dangerouslySetInnerHTML={{ __html: tooltip.content }}
        />
      )}
    </>
  )
})

EnhancedStructuredNotes.displayName = 'EnhancedStructuredNotes'

export default EnhancedStructuredNotes
