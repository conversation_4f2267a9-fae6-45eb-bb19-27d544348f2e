import { create } from 'zustand'

// 文本片段数据结构
export interface TextSegment {
  id: string
  content: string
  startOffset: number
  endOffset: number
  type: 'paragraph' | 'sentence' | 'heading' | 'list-item'
  domPath?: string
  hash: string
  metadata?: {
    level?: number
    listIndex?: number
    parentId?: string
  }
}

// 笔记与原文的关联映射
export interface NoteReference {
  noteId: string
  noteContent: string
  sourceSegments: string[]
  confidence: number
  mappingType: 'exact' | 'semantic' | 'contextual'
  position: {
    startLine: number
    endLine: number
    startChar: number
    endChar: number
  }
}

// 标签页数据类型 - 扩展以支持新的内容系统
export interface TabData {
  id: string
  title: string
  sourceType: 'url' | 'text' // 保持向后兼容
  sourceData: string
  originalContent: string
  aiNoteMarkdown: string
  isLoading: boolean
  aiAnalyzing?: boolean // AI是否正在后台分析
  error?: string
  lastUpdated?: number // 强制重新渲染的时间戳

  // 预留字段 - 为未来的内容系统扩展
  contentType?: string // 内容类型
  contentData?: any // 处理后的内容数据
  processingMetadata?: Record<string, any> // 处理元数据

  // 新增：定位功能相关字段
  textSegments?: TextSegment[] // 原文分段数据
  noteReferences?: NoteReference[] // 笔记与原文的映射关系
  highlightedSegments?: string[] // 当前高亮的片段ID
}

// 聊天消息类型
export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

// 推荐问题类型
export interface RecommendedQuestion {
  id: string
  question: string
  category?: string
}

// 保存的笔记类型
export interface SavedNote {
  id: string
  title: string
  originalContent: string
  structuredNotes: string
  integratedNotes: string
  sourceType: 'url' | 'text'
  sourceData: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  preview?: string // 用于列表显示的预览文本
}

// 应用模式类型
export type AppMode = 'browse' | 'reflection'

// 应用状态接口
interface AppState {
  // 标签页相关
  tabs: TabData[]
  activeTabId: string | null

  // 聊天相关
  chatMessages: Record<string, ChatMessage[]> // tabId -> messages

  // 结构化笔记流式状态
  streamingNotes: Record<string, string> // tabId -> streaming content

  // 推荐问题相关
  recommendedQuestions: Record<string, RecommendedQuestion[]> // tabId -> questions

  // 应用模式相关
  mode: AppMode

  // 沉思模式相关
  savedNotes: SavedNote[]
  selectedNoteId: string | null
  notesLoading: boolean
  notesSearchQuery: string

  // UI状态
  isProcessing: boolean
  
  // Actions
  addTab: (tab: Omit<TabData, 'id'>) => string
  updateTab: (id: string, updates: Partial<TabData>) => void
  removeTab: (id: string) => void
  setActiveTab: (id: string) => void
  
  // 聊天相关actions
  addChatMessage: (tabId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>) => string
  updateChatMessage: (tabId: string, messageId: string, content: string) => void
  clearChatMessages: (tabId: string) => void
  
  // AI笔记流式更新
  updateAINoteStream: (id: string, partialNote: string) => void
  
  // 新的流式笔记管理
  setStreamingNote: (tabId: string, content: string) => void
  clearStreamingNote: (tabId: string) => void
  finalizeStreamingNote: (tabId: string) => void

  // 推荐问题相关actions
  setRecommendedQuestions: (tabId: string, questions: RecommendedQuestion[]) => void
  clearRecommendedQuestions: (tabId: string) => void

  // 模式切换actions
  setMode: (mode: AppMode) => void

  // 沉思模式actions
  loadSavedNotes: (searchQuery?: string) => Promise<void>
  selectNote: (noteId: string | null) => void
  saveNote: (noteData: {
    title: string
    originalContent: string
    structuredNotes: string
    integratedNotes: string
    sourceType: 'url' | 'text'
    sourceData: string
    tags?: string[]
  }) => Promise<SavedNote | null>
  updateNote: (noteId: string, updates: Partial<SavedNote>) => Promise<void>
  deleteNote: (noteId: string) => Promise<void>
  setNotesSearchQuery: (query: string) => void

  // 预留的内容系统actions
  updateTabContentData: (id: string, contentData: any) => void
  updateTabProcessingMetadata: (id: string, metadata: Record<string, any>) => void

  // UI actions
  setProcessing: (processing: boolean) => void
}

// 生成唯一ID - 使用时间戳+随机数确保唯一性
const generateId = () => {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  return `${timestamp}-${random}`
}

// 创建store
export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  tabs: [],
  activeTabId: null,
  chatMessages: {},
  streamingNotes: {},
  recommendedQuestions: {},
  mode: 'browse' as AppMode,
  savedNotes: [],
  selectedNoteId: null,
  notesLoading: false,
  notesSearchQuery: '',
  isProcessing: false,
  
  // 标签页actions
  addTab: (tabData) => {
    const id = generateId()
    const newTab: TabData = {
      id,
      ...tabData,
    }
    
    set((state) => ({
      tabs: [...state.tabs, newTab],
      activeTabId: id,
      chatMessages: {
        ...state.chatMessages,
        [id]: []
      },
      recommendedQuestions: {
        ...state.recommendedQuestions,
        [id]: []
      }
    }))
    
    return id
  },
  
  updateTab: (id, updates) => {
    set((state) => ({
      tabs: state.tabs.map(tab => 
        tab.id === id ? { ...tab, ...updates } : tab
      )
    }))
  },
  
  removeTab: (id) => {
    set((state) => {
      const newTabs = state.tabs.filter(tab => tab.id !== id)
      const newChatMessages = { ...state.chatMessages }
      const newRecommendedQuestions = { ...state.recommendedQuestions }
      delete newChatMessages[id]
      delete newRecommendedQuestions[id]
      
      // 如果删除的是当前活跃标签页，切换到其他标签页
      let newActiveTabId = state.activeTabId
      if (state.activeTabId === id) {
        newActiveTabId = newTabs.length > 0 ? newTabs[newTabs.length - 1].id : null
      }
      
      return {
        tabs: newTabs,
        activeTabId: newActiveTabId,
        chatMessages: newChatMessages,
        recommendedQuestions: newRecommendedQuestions
      }
    })
  },
  
  setActiveTab: (id) => {
    set({ activeTabId: id })
  },
  
  // 聊天actions
  addChatMessage: (tabId, message) => {
    // 验证消息内容 - 允许空字符串（流式聊天占位符）
    if (message.content === null || message.content === undefined || typeof message.content !== 'string') {
      console.error('Invalid message content:', message)
      return ''
    }

    // 清理消息内容，移除可能导致渲染问题的字符
    const cleanContent = message.content.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')

    const messageId = generateId()
    const newMessage: ChatMessage = {
      id: messageId,
      timestamp: new Date(),
      ...message,
      content: cleanContent
    }

    set((state) => ({
      chatMessages: {
        ...state.chatMessages,
        [tabId]: [...(state.chatMessages[tabId] || []), newMessage]
      }
    }))
    
    return messageId
  },
  
  updateChatMessage: (tabId, messageId, content) => {
    set((state) => ({
      chatMessages: {
        ...state.chatMessages,
        [tabId]: (state.chatMessages[tabId] || []).map(msg =>
          msg.id === messageId ? { ...msg, content } : msg
        )
      }
    }))
  },
  
  clearChatMessages: (tabId) => {
    set((state) => ({
      chatMessages: {
        ...state.chatMessages,
        [tabId]: []
      }
    }))
  },
  
  // AI笔记流式更新
  updateAINoteStream: (id, partialNote) => {
    set((state) => ({
      tabs: state.tabs.map(tab =>
        tab.id === id ? { 
          ...tab, 
          aiNoteMarkdown: partialNote,
          lastUpdated: Date.now() // 强制重新渲染
        } : tab
      )
    }))
  },

  // 预留的内容系统actions
  updateTabContentData: (id, contentData) => {
    set((state) => ({
      tabs: state.tabs.map(tab =>
        tab.id === id ? {
          ...tab,
          contentData,
          contentType: contentData.type || tab.contentType,
          title: contentData.title || tab.title,
          originalContent: contentData.content || tab.originalContent
        } : tab
      )
    }))
  },

  updateTabProcessingMetadata: (id, metadata) => {
    set((state) => ({
      tabs: state.tabs.map(tab =>
        tab.id === id ? {
          ...tab,
          processingMetadata: {
            ...tab.processingMetadata,
            ...metadata
          }
        } : tab
      )
    }))
  },

  // UI actions
  setProcessing: (processing) => {
    set({ isProcessing: processing })
  },

  // 新的流式笔记管理
  setStreamingNote: (tabId, content) => {
    set((state) => ({
      streamingNotes: {
        ...state.streamingNotes,
        [tabId]: content
      }
    }))
  },

  clearStreamingNote: (tabId) => {
    set((state) => ({
      streamingNotes: {
        ...state.streamingNotes,
        [tabId]: ''
      }
    }))
  },

  finalizeStreamingNote: (tabId) => {
    set((state) => ({
      streamingNotes: {
        ...state.streamingNotes,
        [tabId]: ''
      }
    }))
  },

  // 推荐问题相关actions
  setRecommendedQuestions: (tabId, questions) => {
    set((state) => ({
      recommendedQuestions: {
        ...state.recommendedQuestions,
        [tabId]: questions
      }
    }))
  },

  clearRecommendedQuestions: (tabId) => {
    set((state) => ({
      recommendedQuestions: {
        ...state.recommendedQuestions,
        [tabId]: []
      }
    }))
  },

  // 模式切换actions
  setMode: (mode) => {
    set({ mode })
  },

  // 沉思模式actions
  loadSavedNotes: async (searchQuery = '') => {
    set({ notesLoading: true })
    try {
      const params = new URLSearchParams()
      if (searchQuery) params.append('search', searchQuery)

      const response = await fetch(`/api/notes?${params}`)
      if (!response.ok) throw new Error('Failed to load notes')

      const data = await response.json()
      set({
        savedNotes: data.notes,
        notesLoading: false
      })
    } catch (error) {
      console.error('Error loading notes:', error)
      set({ notesLoading: false })
    }
  },

  selectNote: (noteId) => {
    set({ selectedNoteId: noteId })
  },

  saveNote: async (noteData) => {
    try {
      const response = await fetch('/api/notes/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(noteData)
      })

      if (!response.ok) throw new Error('Failed to save note')

      const data = await response.json()
      const savedNote = {
        ...data.note,
        tags: data.note.tags ? JSON.parse(data.note.tags) : [],
        createdAt: new Date(data.note.createdAt),
        updatedAt: new Date(data.note.updatedAt)
      }

      // 更新本地状态
      set((state) => ({
        savedNotes: [savedNote, ...state.savedNotes]
      }))

      return savedNote
    } catch (error) {
      console.error('Error saving note:', error)
      return null
    }
  },

  updateNote: async (noteId, updates) => {
    try {
      const response = await fetch(`/api/notes/${noteId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) throw new Error('Failed to update note')

      const data = await response.json()
      const updatedNote = {
        ...data.note,
        tags: data.note.tags ? JSON.parse(data.note.tags) : [],
        createdAt: new Date(data.note.createdAt),
        updatedAt: new Date(data.note.updatedAt)
      }

      // 更新本地状态
      set((state) => ({
        savedNotes: state.savedNotes.map(note =>
          note.id === noteId ? updatedNote : note
        )
      }))
    } catch (error) {
      console.error('Error updating note:', error)
    }
  },

  deleteNote: async (noteId) => {
    try {
      const response = await fetch(`/api/notes/${noteId}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete note')

      // 更新本地状态
      set((state) => ({
        savedNotes: state.savedNotes.filter(note => note.id !== noteId),
        selectedNoteId: state.selectedNoteId === noteId ? null : state.selectedNoteId
      }))
    } catch (error) {
      console.error('Error deleting note:', error)
    }
  },

  setNotesSearchQuery: (query) => {
    set({ notesSearchQuery: query })
  }
}))

// 选择器hooks
export const useActiveTab = () => {
  const { tabs, activeTabId } = useAppStore()
  return tabs.find(tab => tab.id === activeTabId) || null
}

export const useActiveChatMessages = () => {
  const { chatMessages, activeTabId } = useAppStore()
  return activeTabId ? chatMessages[activeTabId] || [] : []
}

// 新的流式笔记选择器
export const useActiveStreamingNote = () => {
  const { streamingNotes, activeTabId } = useAppStore()
  return activeTabId ? streamingNotes[activeTabId] || '' : ''
}

// 推荐问题选择器
export const useActiveRecommendedQuestions = () => {
  const { recommendedQuestions, activeTabId } = useAppStore()
  return activeTabId ? recommendedQuestions[activeTabId] || [] : []
}

// 沉思模式选择器
export const useSelectedNote = () => {
  const { savedNotes, selectedNoteId } = useAppStore()
  return savedNotes.find(note => note.id === selectedNoteId) || null
}

export const useFilteredNotes = () => {
  const { savedNotes, notesSearchQuery } = useAppStore()
  if (!notesSearchQuery) return savedNotes

  const query = notesSearchQuery.toLowerCase()
  return savedNotes.filter(note =>
    note.title.toLowerCase().includes(query) ||
    note.integratedNotes.toLowerCase().includes(query) ||
    note.tags.some(tag => tag.toLowerCase().includes(query))
  )
}

// 预留的选择器hooks - 暂时返回基础数据
export const useActiveTabContentData = () => {
  const activeTab = useActiveTab()
  return activeTab?.contentData || null
}

export const useActiveTabContentType = () => {
  const activeTab = useActiveTab()
  return activeTab?.contentType || activeTab?.sourceType || 'text'
}
